import dayjs from 'dayjs';
import { Before, When } from '@badeball/cypress-cucumber-preprocessor';
import { clickTestIds, getByTestId, type } from '../../../support/utility';
import { logInAsOfficer, updateUserSettings } from '../../../support/actions';

const createAndSubmitForm = (time: string, keepOffline?: boolean) => {
  // Update "User settings" if it appears
  updateUserSettings();

  // Click "New Report" button
  getByTestId('dashboard-filters-new-report').not('.disabled').click();

  // Goto the New report page
  cy.url().should('include', '/new-report');

  if (keepOffline) {
    // Check if login dialog appears when going from offline to online
    // The dialog might not appear immediately, so we'll wait and check if it exists
    cy.get('body').then(($body) => {
      if ($body.find('[data-testid="confirm-dialog-no-button"]').length > 0) {
        // Dialog exists, click the "Continue Offline" button
        getByTestId('confirm-dialog-no-button').click();
        // Wait for dialog to close
        getByTestId('confirm-dialog').should('not.exist');
      } else {
        // Dialog doesn't exist, continue with the test
        cy.log('Login dialog did not appear - continuing with offline form creation');
      }
    });
  }

  // Select "What day did the stop happen?"
  type('ripa-calendar-form-date-picker', dayjs().format('MM/DD/YYYY'));
  getByTestId('ripa-form-container-continue').click();

  // // Select "What time did the stop happen?" (Automatically selected)
  // getByTestId('ripa-form-container-continue').click();
  type('ripa-time-form-input', time);
  getByTestId('ripa-form-container-continue').click();

  // Select "Stop Context"
  clickTestIds(['ripa-response-to-call-yes', 'ripa-form-container-continue'], { multiple: true });

  // Select "How many minutes did it last?"
  clickTestIds(['ripa-duration-form-20', 'ripa-form-container-continue']);

  // Select "Where was the location?"
  clickTestIds(['ripa-tabbed-location-form-location-switch', 'ripa-action-taken-form-action-tab-school', 'ripa-tabbed-location-form-school-autocomplete-input'], { force: true });
  cy.get('*[data-option-index=2]').click();
  getByTestId('ripa-form-container-continue').click();

  // Select "Type of assignment of Officer?"
  clickTestIds(['ripa-type-of-assignment-officer-box-1', 'ripa-form-container-continue']);

  // Select "What was the type of stop?"
  getByTestId('ripa-type-of-stop-select-dropdown').parent().click();
  clickTestIds(['vehicular', 'ripa-form-container-continue']);

  // Select "How many people were stopped by the officer(s)?"
  clickTestIds(['ripa-people-form-people-counter-plus', 'ripa-form-container-continue']);

  // Fill in the form "You can add optional labels to help you remember each of them."
  type('ripa-label-form-label-input-0', 'label 0');
  type('ripa-label-form-label-input-1', 'label 1');
  getByTestId('ripa-form-container-continue').click();

  // Select "What gender were they perceived?"
  clickTestIds(['ripa-gender-form-box-0-0', 'ripa-gender-form-box-1-3', 'ripa-form-container-continue']);

  // Select "What age were they perceived?"
  type('ripa-age-form-input-0', '12');
  getByTestId('ripa-age-form-student-switch-0').click();
  type('ripa-age-form-input-1', '22');
  getByTestId('ripa-form-container-continue').click();

  // Select "What's their perceived race or ethnicity?"
  clickTestIds(['ripa-race-form-0-0', 'ripa-race-form-1-1', 'ripa-form-container-continue']);

  // Select "Were they perceived to be any of these?"
  clickTestIds(['ripa-disability-form-2024-english-switch-1', 'ripa-disability-form-2024-disability-switch-person-1']);
  getByTestId('ripa-disability-form-2024__sexual-orientation-select-0').parent().click();
  clickTestIds(['ripa-disability-form-2024__sexual-orientation-select-0-0', 'ripa-form-container-continue']);

  // Select "What perceived or known disabilities?"
  clickTestIds(['ripa-disability-details-form-box-1-4', 'ripa-form-container-continue']);

  // Select "What was the primary reason for stop?"
  clickTestIds(['ripa-primary-reason-form-box-k12-0', 'ripa-form-container-continue']);

  // Select "What is the code section?"
  clickTestIds(['ripa-code-section-form-box-0', 'ripa-code-section-form-autocomplete-input']);
  cy.get('*[data-option-index=2]').click();
  getByTestId('ripa-form-container-continue').click();

  // Select "What was the reason given to the stopped person?"
  getByTestId('ripa-reason-given-stopped-person-form-box-0').click();
  getByTestId('ripa-form-container-continue').click();

  // Select "Stopped person is a passenger in a vehicle?"
  clickTestIds(['ripa-stopped-passenger-no', 'ripa-form-container-continue']);

  // Fill in the field "Brief description of the stop?"
  type('ripa-description-form-input', '123456', { validate: false }); // No validate since we is typing in a div element
  getByTestId('ripa-form-container-continue').click();

  // Select "What was the primary reason for stop?" - second person
  clickTestIds(['ripa-primary-reason-form-box-3', 'ripa-form-container-continue']);

  // Select "What was the reason given to the stopped person?" - second person
  getByTestId('ripa-reason-given-stopped-person-form-box-0').click();
  getByTestId('ripa-form-container-continue').click({ force: true });

  // Select "Stopped person is a passenger in a vehicle?" - second person
  clickTestIds(['ripa-stopped-passenger-no', 'ripa-form-container-continue']);

  // Fill in the field "Brief description of the stop?" - second person
  type('ripa-description-form-input', '4563456', { validate: false }); // No validate since we is typing in a div element
  getByTestId('ripa-form-container-continue').click();

  // Select "What actions were taken?"
  clickTestIds([
    'ripa-action-taken-form-action-tab-4',
    'ripa-action-taken-form-box-4-0',
    'ripa-action-taken-form-consent-switch-4-0',
    'ripa-action-taken-form-box-4-1',
    'ripa-action-taken-form-box-4-4',
    'ripa-form-container-continue',
  ]);

  // Select "What's the basis for the search?"
  clickTestIds(['ripa-search-basis-form-box-5', 'ripa-form-container-continue']);

  // Fill in the field "Brief description of the search?"
  type('ripa-search-description-form-input', '546456', { validate: false }); // No validate since we is typing in a div element
  getByTestId('ripa-form-container-continue').click();

  // Select "What's the basis for property seizure?"
  clickTestIds(['ripa-seizure-basis-form-box-1', 'ripa-form-container-continue']);

  // Select "What type of property was seized?"
  clickTestIds(['ripa-seizure-type-form-box-1', 'ripa-form-container-continue']);

  // Select "What contraband or evidence was discovered?"
  clickTestIds(['ripa-contraband-form-box-1', 'ripa-form-container-continue']);

  // Select "What was the result of the stop?"
  clickTestIds(['ripa-result-of-stop-form-box-0-0', 'ripa-result-of-stop-form-autocomplete-input-0-0']);
  cy.get('*[data-option-index=10]').click();
  getByTestId('ripa-form-container-continue').click();

  // Select "What actions were taken?" - second person
  clickTestIds(['ripa-action-taken-form-box-0-3', 'ripa-form-container-continue']);

  // Select "What contraband or evidence was discovered?" - second person
  clickTestIds(['ripa-contraband-form-box-5', 'ripa-form-container-continue']);

  // Select "What was the result of the stop?" - second person
  clickTestIds(['ripa-result-of-stop-form-box-0-1', 'ripa-result-of-stop-form-autocomplete-input-0-1']);
  cy.get('*[data-option-index=10]').click();
  getByTestId('ripa-form-container-continue').click();

  // Submit form
  getByTestId('ripa-form-container-continue').click();

  cy.url().should('include', '/dashboard');
};

const createAndSubmitMobileForm = (time: string, keepOffline?: boolean) => {
  // Update "User settings" if it appears
  updateUserSettings();

  // Click "New Report" button
  getByTestId('button-primary').not('.disabled').click();

  // Goto the New report page
  cy.url().should('include', '/new-report');

  if (keepOffline) {
    // Check if login dialog appears when going from offline to online
    // The dialog might not appear immediately, so we'll wait and check if it exists
    cy.get('body').then(($body) => {
      if ($body.find('[data-testid="confirm-dialog-no-button"]').length > 0) {
        // Dialog exists, click the "Continue Offline" button
        getByTestId('confirm-dialog-no-button').click();
        // Wait for dialog to close
        getByTestId('confirm-dialog').should('not.exist');
      } else {
        // Dialog doesn't exist, continue with the test
        cy.log('Login dialog did not appear - continuing with offline form creation');
      }
    });
  }

  // Select "What day did the stop happen?"
  type('ripa-calendar-form-date-picker', dayjs().format('MM/DD/YYYY'));
  getByTestId('ripa-form-container-continue').click();

  // // Select "What time did the stop happen?" (Automatically selected)
  // getByTestId('ripa-form-container-continue').click();
  type('ripa-time-form-input', time);
  getByTestId('ripa-form-container-continue').click();

  // Select "Stop Context"
  clickTestIds(['ripa-response-to-call-yes', 'ripa-form-container-continue'], { multiple: true });

  // Select "How many minutes did it last?"
  clickTestIds(['ripa-duration-form-20', 'ripa-form-container-continue']);

  // Select "Where was the location?"
  clickTestIds(['ripa-tabbed-location-form-location-switch'], { force: true });
  getByTestId('ripa-tabbed-location-form-dropdown').parent().click();
  clickTestIds(['ripa-action-taken-form-action-tab-school', 'ripa-tabbed-location-form-school-autocomplete-input'], { force: true });
  cy.get('*[data-option-index=2]').click();
  clickTestIds(['ripa-form-container-continue']);

  // Select "Type of assignment of Officer?"
  clickTestIds(['ripa-type-of-assignment-officer-box-1', 'ripa-form-container-continue']);

  // Select "What was the type of stop?"
  getByTestId('ripa-type-of-stop-dropdown').select('Vehicular');
  clickTestIds(['vehicular', 'ripa-form-container-continue'], { force: true });

  // Select "How many people were stopped by the officer(s)?"
  clickTestIds(['ripa-people-form-people-counter-plus', 'ripa-form-container-continue']);

  // Fill in the form "You can add optional labels to help you remember each of them."
  type('ripa-label-form-label-input-0', 'label 0');
  type('ripa-label-form-label-input-1', 'label 1');
  getByTestId('ripa-form-container-continue').click();

  // Select "What gender were they perceived?"
  clickTestIds(['ripa-gender-form-box-0-0', 'ripa-gender-form-box-1-3', 'ripa-form-container-continue']);

  // Select "What age were they perceived?"
  type('ripa-age-form-input-0', '12');
  getByTestId('ripa-age-form-student-switch-0').click();
  type('ripa-age-form-input-1', '22');
  getByTestId('ripa-form-container-continue').click();

  // Select "What's their perceived race or ethnicity?"
  clickTestIds(['ripa-race-form-0-0', 'ripa-race-form-1-1', 'ripa-form-container-continue']);

  // Select "Were they perceived to be any of these?"
  clickTestIds(['ripa-disability-form-2024-english-box-0', 'ripa-disability-form-2024-english-box-1']);
  getByTestId('ripa-disability-form-2024__sexual-orientation-mobile-select-0').parent().click();
  clickTestIds(['ripa-disability-form-2024__sexual-orientation-mobile-select-0-0', 'ripa-form-container-continue']);

  // Select "What was the primary reason for stop?"
  clickTestIds(['ripa-primary-reason-form-box-k12-0', 'ripa-form-container-continue']);

  // Select "What is the code section?"
  clickTestIds(['ripa-code-section-form-box-0', 'ripa-code-section-form-autocomplete-input']);
  cy.get('*[data-option-index=2]').click();
  getByTestId('ripa-form-container-continue').click();

  // Select "What was the reason given to the stopped person?"
  getByTestId('ripa-reason-given-stopped-person-form-box-0').click();
  getByTestId('ripa-form-container-continue').click();

  // Select "Stopped person is a passenger in a vehicle?"
  clickTestIds(['ripa-stopped-passenger-no', 'ripa-form-container-continue']);

  // Fill in the field "Brief description of the stop?"
  type('ripa-description-form-input', 'testing1', { validate: false }); // No validate since we is typing in a div element
  getByTestId('ripa-form-container-continue').click();

  // Select "What was the primary reason for stop?" - second person
  clickTestIds(['ripa-primary-reason-form-box-3', 'ripa-form-container-continue']);

  // Select "What was the reason given to the stopped person?" - second person
  getByTestId('ripa-reason-given-stopped-person-form-box-0').click();
  getByTestId('ripa-form-container-continue').click({ force: true });

  // Select "Stopped person is a passenger in a vehicle?" - second person
  clickTestIds(['ripa-stopped-passenger-no', 'ripa-form-container-continue']);

  // Fill in the field "Brief description of the stop?" - second person
  type('ripa-description-form-input', 'testing2', { validate: false }); // No validate since we is typing in a div element
  getByTestId('ripa-form-container-continue').click();

  // Select "What actions were taken?"
  clickTestIds(['ripa-action-taken-form-box-0-0', 'ripa-form-container-continue']);

  // Select "What contraband or evidence was discovered?"
  clickTestIds(['ripa-contraband-form-box-0', 'ripa-form-container-continue']);

  // Select "What was the result of the stop?"
  clickTestIds(['ripa-result-of-stop-form-box-0-0', 'ripa-result-of-stop-form-autocomplete-input-0-0']);
  cy.get('*[data-option-index=5]').click();
  getByTestId('ripa-form-container-continue').click();

  // Select "What actions were taken?" - second person
  clickTestIds(['ripa-action-taken-form-box-0-2', 'ripa-form-container-continue']);

  // Select "What contraband or evidence was discovered?" - second person
  clickTestIds(['ripa-contraband-form-box-1', 'ripa-form-container-continue']);

  // Select "What was the result of the stop?" - second person
  clickTestIds(['ripa-result-of-stop-form-box-0-1', 'ripa-result-of-stop-form-autocomplete-input-0-1']);
  cy.get('*[data-option-index=5]').click();
  getByTestId('ripa-form-container-continue').click();

  // Submit form
  getByTestId('ripa-form-container-continue').click();

  cy.url().should('include', '/dashboard');
};

Before(() => {
  logInAsOfficer();
});

When('The user creates a form and submits it for review', () => {
  cy.viewport(1000, 1000);
  createAndSubmitForm('00:12');
});

When('The user creates a mobile form and submits it for review', () => {
  cy.viewport(414, 896);
  getByTestId('header-navigation').should('exist');
  cy.get('div.auth-provider__loading').should('not.exist');
  clickTestIds(['header-navigation', 'header__veritone-nav-my-reports']);
  createAndSubmitMobileForm('00:15');
});
