import dayjs from 'dayjs';
import {clickTestIds, getByTestId, type, typeLogin } from './utility';

export const logInAs = (username: string, password: string, landingPage: string | null = null, subdomain = 'demo'): void => {
  cy.session(
    [username, password, landingPage, subdomain],
    () => {
      cy.visit(`https://${subdomain || 'demo'}.contact-dev.com:2222/log_in`);
      typeLogin('login-username-field', username);
      typeLogin('login-pw-field', password);

      getByTestId('login-submit').click();
      cy.url().should('not.include', '/log_in');
      if (landingPage) {
        cy.url().should('include', landingPage);
      }
    },
    {
      validate() {
        cy.request('/api/v1/users/session').its('status').should('eq', 200);
      },
    }
  );
  cy.visit(`https://${subdomain}.contact-dev.com:2222${landingPage || '/'}`);
  getByTestId('header-navigation').should('exist');
  cy.get('div.auth-provider__loading').should('not.exist');
  getByTestId('loading').should('not.exist');
};

export const logInAsReviewer = (): void => {
  logInAs('demo_reviewer_1', 'ripa_demo', '/review');
};

export const logInAsOfficer = (): void => {
  logInAs('demo_officer_1', 'ripa_demo', '/dashboard');
};

export const logInAsAdmin = (): void => {
  logInAs('demo_admin_1', 'ripa_demo', '/admin/users');
};

export const logInAsSuperAdmin = (): void => {
  logInAs('veritone_cs', 'ripa_demo', '/admin/users', 'veri-admin');
};

export const updateUserSettings = (): void => {
  cy.wait(1000);

  cy.get('body').then((bodyElement) => {
    if (bodyElement.find('[data-testid="confirm-dialog"]').length > 0) {
      getByTestId('race-of-officer-select').click();
      getByTestId('race-of-officer-select-checkbox-1').click();
      getByTestId('confirm-dialog-yes-button').click({ force: true });
    }
  });
};

export const createAndSubmitForm = (keepOffline: boolean, time: string): void => {
  updateUserSettings();
  getByTestId('dashboard-filters-new-report').not('.disabled').click();
  cy.url().should('include', '/new-report');

  if (keepOffline) {
    // Check if login dialog appears when going from offline to online
    // The dialog might not appear immediately, so we'll wait and check if it exists
    cy.get('body').then(($body) => {
      if ($body.find('[data-testid="confirm-dialog-no-button"]').length > 0) {
        // Dialog exists, click the "Continue Offline" button
        getByTestId('confirm-dialog-no-button').click();
        // Wait for dialog to close
        getByTestId('confirm-dialog').should('not.exist');
      } else {
        // Dialog doesn't exist, continue with the test
        cy.log('Login dialog did not appear - continuing with offline form creation');
      }
    });
  }
  type('ripa-calendar-form-date-picker', dayjs().format('MM/DD/YYYY'));
  getByTestId('ripa-form-container-continue').click();
  type('ripa-time-form-input', time);
  getByTestId('ripa-form-container-continue').click();
  clickTestIds(['ripa-response-to-call-yes', 'ripa-form-container-continue', 'ripa-duration-form-20']);
  // clearSnackBars();
  clickTestIds(['ripa-form-container-continue']);
  cy.get('[data-testid="ripa-action-taken-form-action-tab-school"]').click();
  cy.get('[data-testid="ripa-tabbed-location-form-school-autocomplete-input"]')
  .click();
  cy.get('*[data-option-index=2]').click();
  getByTestId('ripa-form-container-continue').click();
  cy.get('[data-testid="ripa-type-of-assignment-officer-box-0"]').click();
  getByTestId('ripa-form-container-continue').click();
  getByTestId('ripa-form-container-continue').click();
  cy.get('[data-testid="ripa-type-of-stop-select-dropdown"]').click();
  cy.get('ul li').eq(1).click();
  getByTestId('ripa-form-container-continue').click();
  cy.get('[data-testid="ripa-people-form-people-counter-number"]').contains('1');
  getByTestId('ripa-form-container-continue').click();
  cy.get('[data-testid="ripa-gender-form-box-0-0"]').click();
  getByTestId('ripa-form-container-continue').click();
  cy.get('[data-testid="ripa-age-form-input-0"]').type('1');
  getByTestId('ripa-form-container-continue').click();
  cy.get('[data-testid="ripa-race-form-0-0"]').click();
  getByTestId('ripa-form-container-continue').click();
  cy.get('[data-testid="ripa-disability-form-2024__sexual-orientation-select-0"]').click();
  cy.get('ul li').eq(1).click();
  getByTestId('ripa-form-container-continue').click();
  cy.get('[data-testid="ripa-primary-reason-form-box-0"]').click();

  getByTestId('ripa-form-container-continue').click();
  cy.get('[data-testid="ripa-traffic-violation-form-autocomplete-input"]').type('32 - ACCESSORY');
  cy.get('[data-testid="ripa-traffic-violation-form-box-0"]').click();
  getByTestId('ripa-form-container-continue').click({force: true});
  cy.get('[data-testid="ripa-reason-given-stopped-person-form-box-0"]').click();
  
  getByTestId('ripa-form-container-continue').click();
  getByTestId('ripa-form-container-continue').click();
  cy.get('[data-testid="pii-text-field"]').type('test 1');
  getByTestId('ripa-form-container-continue').click();
  cy.get('[data-testid="ripa-action-taken-form-box-0-0"]')
  getByTestId('ripa-form-container-continue').click();
  cy.get('[data-testid="ripa-action-taken-form-box-0-0"]').click();
  getByTestId('ripa-form-container-continue').click();
  cy.get('[data-testid="ripa-contraband-form-box-0"]').click();
  getByTestId('ripa-form-container-continue').click();
  cy.get('[data-testid="ripa-result-of-stop-form-box-0-0"]').click();
  getByTestId('ripa-form-container-continue').click();
  cy.get('[data-testid="ripa-result-of-stop-form-autocomplete-input-0-0"]').click();
  cy.get('ul li').eq(1).click();
  getByTestId('ripa-form-container-continue').click();
  cy.get('[data-testid="ripa-form-container-continue"]').click();
  cy.url().should('include', '/dashboard');
};

export const createFormThroughStep4 = (keepOffline: boolean, time: string): void => {
  getByTestId('dashboard-filters-new-report').not('.disabled').click();

  cy.url().should('include', '/new-report');

  if (keepOffline) {
    // Wait for the dialog and continue offline
    getByTestId('confirm-dialog-no-button', { timeout: 6000 }).click();
    getByTestId('confirm-dialog-no-button').should('not.be', 'visible');
    // cy.wait(1000);
  }

  type('ripa-calendar-form-date-picker', dayjs().format('MM/DD/YYYY'));

  getByTestId('ripa-form-container-continue').click();
  
  type('ripa-time-form-input', time);
  getByTestId('ripa-form-container-continue').click();

  if (keepOffline) {
    // Wait for the dialog and continue offline
    getByTestId('confirm-dialog-no-button', { timeout: 6000 }).click();
    getByTestId('confirm-dialog-no-button').should('not.be', 'visible');
    // cy.wait(1000);
  }

  clickTestIds([
    'ripa-form-container-continue',
    'ripa-response-to-call-yes',
    'ripa-form-container-continue',
    'ripa-duration-form-20',
    'ripa-form-container-continue',
    // 'ripa-location-form-school-switch',
    // 'ripa-location-form-school-autocomplete',
    'ripa-action-taken-form-action-tab-school',
    'ripa-tabbed-location-form-school-autocomplete-input'
  ]);

  cy.get('*[data-option-index=2]').click();

  // clearSnackBars();

  getByTestId('ripa-form-container-continue').click();

  //type of assignment
  cy.get('[data-testid="ripa-type-of-assignment-officer-box-0"]').click();
  getByTestId('ripa-form-container-continue').click();
  getByTestId('ripa-form-container-continue').click();

  //type of stop
  cy.get('[data-testid="ripa-type-of-stop-select-dropdown"]').click();
  cy.get('ul li').eq(1).click();
  getByTestId('ripa-form-container-continue').click();

  getByTestId('ripa-people-form-people-counter-plus').click();

  getByTestId('ripa-form-container-continue').click();

  type('ripa-label-form-label-input-0', 'label 0');

  type('ripa-label-form-label-input-1', 'label 1');

  getByTestId('ripa-form-container-continue').click();

  getByTestId('ripa-gender-form-box-0-0').click();

  getByTestId('ripa-gender-form-box-1-3').click();

  getByTestId('ripa-form-container-continue').click();

  type('ripa-age-form-input-0', '12');

  getByTestId('ripa-age-form-student-switch-0').click();

  type('ripa-age-form-input-1', '22');

  clickTestIds([
    'ripa-form-container-continue',
    'ripa-race-form-0-0',
    'ripa-race-form-1-1',
    'ripa-form-container-continue',
    'ripa-disability-form-2024-english-switch-1',
    'ripa-disability-form-2024-disability-switch-person-1',
  ]);

  getByTestId('ripa-disability-form-2024__sexual-orientation-select-0').click();
  cy.get('ul li').eq(0).click();

    clickTestIds([
    'ripa-form-container-continue',
    'ripa-disability-details-form-box-1-4',
    'ripa-form-container-continue',
    'ripa-primary-reason-form-box-k12-0',
    'ripa-form-container-continue',
    'ripa-code-section-form-box-0',
    'ripa-code-section-form-autocomplete-input',
  ]);

  cy.get('*[data-option-index=2]').click();

  getByTestId('ripa-form-container-continue').click();

  // Select "What was the reason given to the stopped person?"
  getByTestId('ripa-reason-given-stopped-person-form-box-0').click();
  getByTestId('ripa-form-container-continue').click();

  // Select "Stopped person is a passenger in a vehicle?"
  clickTestIds(['ripa-stopped-passenger-no', 'ripa-form-container-continue']);

  getByTestId('ripa-description-form-input').type('123456');
  getByTestId('ripa-description-form-input').should('contain.text','123456');
  getByTestId('ripa-form-container-continue').click();

  getByTestId('ripa-primary-reason-form-box-3').click();

  getByTestId('ripa-form-container-continue').click();

  // Select "What was the reason given to the stopped person?"
  getByTestId('ripa-reason-given-stopped-person-form-box-0').click();
  getByTestId('ripa-form-container-continue').click();

  // Select "Stopped person is a passenger in a vehicle?"
  clickTestIds(['ripa-stopped-passenger-no', 'ripa-form-container-continue']);

  getByTestId('ripa-description-form-input').type('4563456');
  getByTestId('ripa-description-form-input').should('contain.text','4563456');
  clickTestIds([
    'ripa-form-container-continue',
    'ripa-action-taken-form-action-tab-4',
    'ripa-action-taken-form-box-4-0',
    'ripa-action-taken-form-consent-switch-4-0',
    'ripa-action-taken-form-box-4-1',
    'ripa-action-taken-form-box-4-4',
    'ripa-form-container-continue',
    'ripa-search-basis-form-box-5',
    'ripa-form-container-continue',
  ]);

  getByTestId('ripa-search-description-form-input').type('546456');
  getByTestId('ripa-search-description-form-input').should('contain.text','546456');
  clickTestIds([
    'ripa-form-container-continue',
    'ripa-seizure-basis-form-box-1',
    'ripa-form-container-continue',
    'ripa-seizure-type-form-box-1',
    'ripa-form-container-continue',
    'ripa-contraband-form-box-1',
    'ripa-form-container-continue',
    'ripa-result-of-stop-form-box-0-0',
    'ripa-result-of-stop-form-autocomplete-input-0-0',
  ]);

  cy.get('*[data-option-index=10]').click();

  clickTestIds([
    'ripa-form-container-continue',
    'ripa-action-taken-form-box-0-3',
    'ripa-form-container-continue',
    'ripa-contraband-form-box-5',
    'ripa-form-container-continue',
    'ripa-result-of-stop-form-box-0-1',
    'ripa-result-of-stop-form-autocomplete-input-0-1',
  ]);

  cy.get('*[data-option-index=10]').click();

  getByTestId('ripa-form-container-continue').click();
};
