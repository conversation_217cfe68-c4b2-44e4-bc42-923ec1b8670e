Cypress.Commands.add(
  "mockGeolocation",
  (coords: { latitude: number; longitude: number }) => {
    cy.window().then((win) => {
      cy.wrap(
        Cypress.automation("remote:debugger:protocol", {
          command: "Browser.grantPermissions",
          params: {
            permissions: ["geolocation"],
            origin: win.location.origin,
          },
        }),
      );
    });
    
    console.debug(
      `cypress::setGeolocationOverride with position ${JSON.stringify(coords)}`,
    );
    
    cy.log("**setGeolocationOverride**").then(() =>
      Cypress.automation("remote:debugger:protocol", {
        command: "Emulation.setGeolocationOverride",
        params: {
          latitude: coords.latitude,
          longitude: coords.longitude,
          accuracy: 50,
        },
      }),
    );
  },
);
