{"extends": "../tsconfig.json", "compilerOptions": {"target": "es5", "module": "ESNext", "lib": ["es2019", "dom", "dom.iterable", "scripthost", "webworker"], "baseUrl": "./", "typeRoots": ["../node_modules", "../node_modules/@types", "../node_modules/veritone-types/@types", "../types"], "types": ["cypress", "node"], "paths": {"@engine/*": ["../src/engine/*"], "@ducks/*": ["../src/engine/ducks/*"], "@ducks": ["../src/engine/ducks/index.ts"], "@pages/*": ["../src/pages/*"], "@utility/*": ["../src/utility/*"], "@components/*": ["../src/components/*"], "@src/*": ["../src/*"], "@theme/*": ["../src/theme/*"]}}, "include": ["**/*.js", "**/*.ts", "**/*.tsx", "./src/types.d.ts"], "exclude": ["node_modules"]}