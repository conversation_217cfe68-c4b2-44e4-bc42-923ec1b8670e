import { Before, Given, When } from '@badeball/cypress-cucumber-preprocessor';
import { getByTestId, type as typeIntoTestId } from '../../../support/utility';
import { logInAsReviewer, logInAsOfficer, createAndSubmitForm } from '../../../support/actions';

const expandFirstRow = () => {
  getByTestId('review-row-cell-select-expand').first().click();

  cy.get('.expanded').first().should('be.visible');
  // cy.wait(1000);
};

// const testReviewerId = (reportIndex) => (
//   cy.window().its('store').invoke('getState')
//     .its('Review')
//     .its('userForms')
//     .its(reportIndex)
//     .its('contents')
//     .its('reviewerId').should('eq', 6)
// )

const openReviewDialog = () => {
  getByTestId('review-row-table-cell-actions-review').first().click();

  getByTestId('review-dialog').should('exist');
};

const approveReport = () => {
  getByTestId('review-dialog').scrollTo('bottom');
  getByTestId('review-dialog-approve-bottom').click();

  getByTestId('review-dialog').should('not.exist');
};

const denyReport = () => {
  getByTestId('review-dialog').scrollTo('bottom');
  getByTestId('review-dialog-deny-bottom').click();

  typeIntoTestId('review-feedback-dialog-input', 'Error 101');

  getByTestId('confirm-dialog-yes-button').click();

  getByTestId('review-dialog').should('not.exist');
};

const editReportLocation = () => {
  getByTestId('review-row-cell-select-expand').not('.disabled').first().click();

  getByTestId('review-row-row-details-stop-field-input').clear()
    .not('.disabled')
    .clear()
    .type('2021 Webster Street, Oakland, CA 9999-3333')
    .should('contain', '2021 Webster Street, Oakland, CA 9999-3333');
};

const editStopFieldDescription = () => {
  expandFirstRow();

  cy.get('.expanded').first().should('be.visible');

  getByTestId('review-row-row-details-stop-field-input').clear()
  .type('Stop description automation test value')
  getByTestId('review-row-row-details-stop-field-input').should('contain', 'Stop description automation test value');

  cy.wait(3000); // Some time for data to save
};

const editStopFieldDescriptionMultipleTimes = () => {
  expandFirstRow();

  cy.get('.expanded').first().should('be.visible');

  getByTestId('review-row-row-details-stop-field-input').as('input').clear()
  .type('Stop description automation test value 1')
  .should('contain', 'Stop description automation test value 1');

  cy.get('@input').clear().type('Stop description automation test value 2');
  cy.get('@input').should('contain', 'Stop description automation test value 2');

  cy.wait(3000);
};

Before(() => {
  logInAsOfficer();
});

Given('The user creates a form and submits it for review with time {string}', (time: string) => {
  createAndSubmitForm(false, time);

  cy.wait(3000); // Wait to ensure it's saved???

  logInAsReviewer();
});

When('The user opens the review dialog', () => {
  openReviewDialog();
});

When('The user approves a report', () => {
  getByTestId('review-filters-under-review').click();
  getByTestId('loading').should('not.exist');

  openReviewDialog();

  approveReport();
});

When('The user approves a report by clicking the approve icon', () => {
  getByTestId('review-filters-under-review').click();
  getByTestId('loading').should('not.exist');

  getByTestId('review-row-table-cell-approve').not('.disabled').first().click();

  getByTestId('review-dialog').should('not.exist');

  // testReviewerId(2);
});

When('The user denies a report', () => {
  getByTestId('review-filters-under-review').click();
  getByTestId('loading').should('not.exist');

  openReviewDialog();

  denyReport();
});

When('The user edits a report location', () => {
  getByTestId('review-filters-under-review').click();
  getByTestId('loading').should('not.exist');

  editReportLocation();
});

When('The user edits a stop field description', () => {
  getByTestId('review-filters-under-review').click();
  getByTestId('loading').should('not.exist');

  editStopFieldDescription();

  // What's the point in logging out here?
  getByTestId('header-navigation').click();

  getByTestId('header-veritone-nav-signout').click();

  cy.url().should('include', '/log_in');

  logInAsReviewer();

  expandFirstRow();
  getByTestId('review-row-row-details-stop-field-input').should('contain', 'Stop description automation test value');
});

When('The user edits a stop field description multiple times', () => {
  getByTestId('review-filters-under-review').click();
  getByTestId('loading').should('not.exist');

  editStopFieldDescriptionMultipleTimes();

  getByTestId('header-navigation').click();

  getByTestId('header-veritone-nav-signout').click();

  cy.url().should('include', 'log_in');

  logInAsReviewer();

  expandFirstRow();

  getByTestId('review-row-row-details-stop-field-input').should('contain', 'Stop description automation test value 2');
});
