Feature: Review

  @e2e @review
  Scenario: The user opens the review dialog
    Given The user creates a form and submits it for review with time "00:04"
    When The user opens the review dialog

  @e2e @review
  Scenario: The user approves a report
    Given The user creates a form and submits it for review with time "00:05"
    When The user approves a report

  @e2e @review
  Scenario: The user approves a report by clicking the approve icon
    Given The user creates a form and submits it for review with time "00:06"
    When The user approves a report by clicking the approve icon

  @e2e @review
  Scenario: The user denies a report
    Given The user creates a form and submits it for review with time "00:07"
    When The user denies a report

  @e2e @review
  Scenario: The user edits a report location
    Given The user creates a form and submits it for review with time "00:08"
    When The user edits a report location

  @e2e @review
  Scenario: The user edits a stop field description
    Given The user creates a form and submits it for review with time "00:09"
    When The user edits a stop field description

  @e2e @review
  Scenario: The user edits a stop field description multiple times
    Given The user creates a form and submits it for review with time "00:10"
    When The user edits a stop field description multiple times
